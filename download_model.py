#!/usr/bin/env python3
"""
下载预训练的YOLO模型
"""

import os
import sys
import urllib.request
from pathlib import Path

def download_yolo_model():
    """下载YOLOv11n预训练模型"""
    models_dir = Path("models")
    models_dir.mkdir(exist_ok=True)
    
    # YOLOv11n模型URL
    model_url = "https://github.com/ultralytics/assets/releases/download/v8.3.0/yolo11n.pt"
    model_path = models_dir / "yolo11n.pt"
    
    # 如果模型已存在，跳过下载
    if model_path.exists():
        print(f"模型已存在: {model_path}")
        return str(model_path)
    
    print(f"正在下载YOLO模型: {model_url}")
    print(f"保存到: {model_path}")
    
    try:
        # 下载模型
        urllib.request.urlretrieve(model_url, model_path)
        print(f"模型下载完成: {model_path}")
        
        # 创建best.pt的符号链接
        best_path = models_dir / "best.pt"
        if not best_path.exists():
            # 在Windows上创建硬链接
            if os.name == 'nt':
                os.link(model_path, best_path)
            else:
                os.symlink(model_path, best_path)
            print(f"创建默认模型链接: {best_path}")
        
        return str(model_path)
        
    except Exception as e:
        print(f"下载失败: {e}")
        return None

if __name__ == "__main__":
    download_yolo_model()
