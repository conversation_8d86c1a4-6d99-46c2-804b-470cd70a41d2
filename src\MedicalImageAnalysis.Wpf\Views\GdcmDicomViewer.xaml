<UserControl x:Class="MedicalImageAnalysis.Wpf.Views.GdcmDicomViewer"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             mc:Ignorable="d"
             d:DesignHeight="600" d:DesignWidth="800"
             Background="Black">

    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- 工具栏 -->
        <Border Grid.Row="0" Background="#2D2D30" Padding="10,5">
            <StackPanel Orientation="Horizontal">
                <Button Name="OpenFileButton" Content="打开DICOM文件" 
                        Padding="10,5" Margin="0,0,10,0"
                        Background="#007ACC" Foreground="White"
                        BorderBrush="#007ACC" Click="OpenFileButton_Click"/>
                
                <Separator Margin="10,0" Background="#555"/>
                
                <TextBlock Text="窗宽:" Foreground="White" VerticalAlignment="Center" Margin="10,0,5,0"/>
                <TextBox Name="WindowWidthTextBox" Width="80" Text="400" 
                         VerticalAlignment="Center" Margin="0,0,10,0"
                         TextChanged="WindowLevel_TextChanged"/>
                
                <TextBlock Text="窗位:" Foreground="White" VerticalAlignment="Center" Margin="10,0,5,0"/>
                <TextBox Name="WindowCenterTextBox" Width="80" Text="40" 
                         VerticalAlignment="Center" Margin="0,0,10,0"
                         TextChanged="WindowLevel_TextChanged"/>
                
                <Button Name="ResetWindowButton" Content="重置窗宽窗位"
                        Padding="8,5" Margin="10,0,0,0"
                        Background="#555" Foreground="White"
                        BorderBrush="#555" Click="ResetWindowButton_Click"/>

                <Separator Margin="10,0" Background="#555"/>

                <!-- 窗宽窗位预设 -->
                <TextBlock Text="预设:" Foreground="White" VerticalAlignment="Center" Margin="10,0,5,0"/>
                <Button Name="LungWindowButton" Content="肺窗" Padding="6,5" Margin="0,0,5,0"
                        Background="#007ACC" Foreground="White" BorderBrush="#007ACC"
                        Click="LungWindowButton_Click"/>
                <Button Name="SoftTissueWindowButton" Content="软组织窗" Padding="6,5" Margin="0,0,5,0"
                        Background="#007ACC" Foreground="White" BorderBrush="#007ACC"
                        Click="SoftTissueWindowButton_Click"/>
                <Button Name="BoneWindowButton" Content="骨窗" Padding="6,5" Margin="0,0,5,0"
                        Background="#007ACC" Foreground="White" BorderBrush="#007ACC"
                        Click="BoneWindowButton_Click"/>
                <Button Name="BrainWindowButton" Content="脑窗" Padding="6,5" Margin="0,0,10,0"
                        Background="#007ACC" Foreground="White" BorderBrush="#007ACC"
                        Click="BrainWindowButton_Click"/>

                <Separator Margin="10,0" Background="#555"/>

                <!-- 亮度调整 -->
                <TextBlock Text="亮度:" Foreground="White" VerticalAlignment="Center" Margin="10,0,5,0"/>
                <TextBox Name="BrightnessTextBox" Width="50" Text="0" Margin="0,0,5,0"
                         VerticalAlignment="Center" TextAlignment="Center"
                         KeyDown="BrightnessTextBox_KeyDown"/>
                <TextBlock Text="%" Foreground="White" VerticalAlignment="Center" Margin="0,0,5,0"/>

                <Button Name="ResetBrightnessButton" Content="重置亮度"
                        Padding="6,5" Margin="0,0,10,0"
                        Background="#555" Foreground="White"
                        BorderBrush="#555" Click="ResetBrightnessButton_Click"/>

                <Separator Margin="10,0" Background="#555"/>

                <Button Name="ExportImageButton" Content="导出图像"
                        Padding="8,5" Margin="10,0,0,0"
                        Background="#28A745" Foreground="White"
                        BorderBrush="#28A745" Click="ExportImageButton_Click"/>

                <Separator Margin="10,0" Background="#555"/>

                <Button Name="AIDetectionButton" Content="AI检测"
                        Padding="8,5" Margin="10,0,0,0"
                        Background="#FF6B35" Foreground="White"
                        BorderBrush="#FF6B35" Click="AIDetectionButton_Click"/>
            </StackPanel>
        </Border>

        <!-- 主显示区域 -->
        <Grid Grid.Row="1">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="Auto"/>
                <ColumnDefinition Width="300"/>
            </Grid.ColumnDefinitions>

            <!-- 图像显示区域 -->
            <Border Grid.Column="0" Background="Black" BorderBrush="#555" BorderThickness="1">
                <ScrollViewer Name="ImageScrollViewer"
                              CanContentScroll="True"
                              HorizontalScrollBarVisibility="Auto"
                              VerticalScrollBarVisibility="Auto"
                              Background="Black">
                    <Grid>
                        <Image Name="DicomImage"
                               Stretch="Uniform"
                               RenderOptions.BitmapScalingMode="NearestNeighbor"/>
                        <Canvas Name="ImageCanvas" Background="Transparent"/>
                    </Grid>
                </ScrollViewer>
            </Border>

            <!-- 分隔符 -->
            <GridSplitter Grid.Column="1" Width="5" Background="#555" 
                          HorizontalAlignment="Center" VerticalAlignment="Stretch"/>

            <!-- 信息面板 -->
            <Border Grid.Column="2" Background="#2D2D30" BorderBrush="#555" BorderThickness="1,0,0,0">
                <ScrollViewer VerticalScrollBarVisibility="Auto">
                    <StackPanel Margin="10">
                        <!-- DICOM信息 -->
                        <TextBlock Text="DICOM 信息" FontWeight="Bold" FontSize="14" 
                                   Foreground="White" Margin="0,0,0,10"/>
                        
                        <ListView Name="DicomInfoListView" 
                                  Background="Transparent" 
                                  BorderThickness="0"
                                  Foreground="White">
                            <ListView.View>
                                <GridView>
                                    <GridViewColumn Header="标签" Width="120">
                                        <GridViewColumn.CellTemplate>
                                            <DataTemplate>
                                                <TextBlock Text="{Binding Key}" Foreground="White"/>
                                            </DataTemplate>
                                        </GridViewColumn.CellTemplate>
                                    </GridViewColumn>
                                    <GridViewColumn Header="值" Width="150">
                                        <GridViewColumn.CellTemplate>
                                            <DataTemplate>
                                                <TextBlock Text="{Binding Value}" Foreground="LightGray" 
                                                           TextWrapping="Wrap"/>
                                            </DataTemplate>
                                        </GridViewColumn.CellTemplate>
                                    </GridViewColumn>
                                </GridView>
                            </ListView.View>
                        </ListView>

                        <!-- 图像统计信息 -->
                        <TextBlock Text="图像统计" FontWeight="Bold" FontSize="14" 
                                   Foreground="White" Margin="0,20,0,10"/>
                        
                        <StackPanel Name="StatisticsPanel">
                            <TextBlock Name="MeanValueText" Foreground="LightGray" Margin="0,2"/>
                            <TextBlock Name="MinValueText" Foreground="LightGray" Margin="0,2"/>
                            <TextBlock Name="MaxValueText" Foreground="LightGray" Margin="0,2"/>
                            <TextBlock Name="StdDevText" Foreground="LightGray" Margin="0,2"/>
                        </StackPanel>

                        <!-- 当前窗宽窗位 -->
                        <TextBlock Text="当前设置" FontWeight="Bold" FontSize="14" 
                                   Foreground="White" Margin="0,20,0,10"/>
                        
                        <StackPanel>
                            <TextBlock Name="CurrentWindowText" Foreground="LightGray" Margin="0,2"/>
                            <TextBlock Name="CurrentExposureText" Foreground="LightGray" Margin="0,2"/>
                            <TextBlock Name="ZoomLevelText" Foreground="LightGray" Margin="0,2"/>
                            <TextBlock Name="ImageSizeText" Foreground="LightGray" Margin="0,2"/>
                            <TextBlock Name="PixelRangeText" Foreground="LightGray" Margin="0,2"/>
                        </StackPanel>
                    </StackPanel>
                </ScrollViewer>
            </Border>
        </Grid>

        <!-- 状态栏 -->
        <Border Grid.Row="2" Background="#007ACC" Padding="10,5">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>
                
                <TextBlock Name="StatusText" 
                           Text="就绪 - 请打开DICOM文件" 
                           Foreground="White" 
                           VerticalAlignment="Center"/>
                
                <StackPanel Grid.Column="1" Orientation="Horizontal">
                    <TextBlock Name="ProgressText" 
                               Foreground="White" 
                               VerticalAlignment="Center" 
                               Margin="0,0,10,0"/>
                    <ProgressBar Name="ProgressBar" 
                                 Width="100" Height="15" 
                                 Visibility="Collapsed"/>
                </StackPanel>
            </Grid>
        </Border>
    </Grid>
</UserControl>
