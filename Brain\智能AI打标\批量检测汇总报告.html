﻿
<!DOCTYPE html>
<html lang='zh-CN'>
<head>
    <meta charset='UTF-8'>
    <meta name='viewport' content='width=device-width, initial-scale=1.0'>
    <title>批量AI检测汇总报告</title>
    <style>
        body { font-family: 'Microsoft YaHei', Arial, sans-serif; margin: 20px; background-color: #f5f5f5; }
        .container { max-width: 1200px; margin: 0 auto; background: white; padding: 30px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .header { text-align: center; margin-bottom: 30px; padding-bottom: 20px; border-bottom: 2px solid #007acc; }
        .header h1 { color: #007acc; margin: 0; font-size: 28px; }
        .header p { color: #666; margin: 10px 0 0 0; font-size: 16px; }
        .summary { display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 20px; margin-bottom: 30px; }
        .summary-card { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 20px; border-radius: 8px; text-align: center; }
        .summary-card.success { background: linear-gradient(135deg, #4CAF50 0%, #45a049 100%); }
        .summary-card.failed { background: linear-gradient(135deg, #f44336 0%, #da190b 100%); }
        .summary-card.detections { background: linear-gradient(135deg, #ff9800 0%, #f57c00 100%); }
        .summary-card h3 { margin: 0 0 10px 0; font-size: 18px; }
        .summary-card .number { font-size: 32px; font-weight: bold; margin: 10px 0; }
        .section { margin-bottom: 30px; }
        .section h2 { color: #333; border-bottom: 2px solid #007acc; padding-bottom: 10px; }
        table { width: 100%; border-collapse: collapse; margin-top: 15px; }
        th, td { padding: 12px; text-align: left; border-bottom: 1px solid #ddd; }
        th { background-color: #007acc; color: white; font-weight: bold; }
        tr:nth-child(even) { background-color: #f9f9f9; }
        tr:hover { background-color: #f5f5f5; }
        .status-success { color: #4CAF50; font-weight: bold; }
        .status-failed { color: #f44336; font-weight: bold; }
        .footer { text-align: center; margin-top: 30px; padding-top: 20px; border-top: 1px solid #ddd; color: #666; }
    </style>
</head>
<body>
    <div class='container'>
        <div class='header'>
            <h1>🔍 批量AI检测汇总报告</h1>
            <p>生成时间: 2025年07月30日 13:23:26</p>
        </div>

        <div class='summary'>
            <div class='summary-card'>
                <h3>总文件数</h3>
                <div class='number'>10</div>
            </div>
            <div class='summary-card success'>
                <h3>成功处理</h3>
                <div class='number'>0</div>
            </div>
            <div class='summary-card failed'>
                <h3>处理失败</h3>
                <div class='number'>10</div>
            </div>
            <div class='summary-card detections'>
                <h3>总检测数</h3>
                <div class='number'>0</div>
            </div>
        </div>
        <div class='section'>
            <h2>❌ 处理失败的文件</h2>
            <table>
                <thead>
                    <tr>
                        <th>文件名</th>
                        <th>失败原因</th>
                        <th>处理时间</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td>DJ01.dcm</td>
                        <td class='status-failed'>AI标注未检测到任何目标</td>
                        <td>13:23:23</td>
                    </tr>
                    <tr>
                        <td>DJ02.dcm</td>
                        <td class='status-failed'>AI标注未检测到任何目标</td>
                        <td>13:23:24</td>
                    </tr>
                    <tr>
                        <td>DJ03.dcm</td>
                        <td class='status-failed'>AI标注未检测到任何目标</td>
                        <td>13:23:24</td>
                    </tr>
                    <tr>
                        <td>DJ04.dcm</td>
                        <td class='status-failed'>AI标注未检测到任何目标</td>
                        <td>13:23:24</td>
                    </tr>
                    <tr>
                        <td>DJ05.dcm</td>
                        <td class='status-failed'>AI标注未检测到任何目标</td>
                        <td>13:23:24</td>
                    </tr>
                    <tr>
                        <td>DJ06.dcm</td>
                        <td class='status-failed'>AI标注未检测到任何目标</td>
                        <td>13:23:25</td>
                    </tr>
                    <tr>
                        <td>DJ07.dcm</td>
                        <td class='status-failed'>AI标注未检测到任何目标</td>
                        <td>13:23:25</td>
                    </tr>
                    <tr>
                        <td>DJ08.dcm</td>
                        <td class='status-failed'>AI标注未检测到任何目标</td>
                        <td>13:23:25</td>
                    </tr>
                    <tr>
                        <td>DJ09.dcm</td>
                        <td class='status-failed'>AI标注未检测到任何目标</td>
                        <td>13:23:26</td>
                    </tr>
                    <tr>
                        <td>DJ10.dcm</td>
                        <td class='status-failed'>AI标注未检测到任何目标</td>
                        <td>13:23:26</td>
                    </tr>
                </tbody>
            </table>
        </div>
        <div class='footer'>
            <p>📊 报告由医学影像解析系统自动生成</p>
        </div>
    </div>
</body>
</html>