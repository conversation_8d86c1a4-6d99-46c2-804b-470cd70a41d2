using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Input;
using System.Windows.Media.Imaging;
using System.Windows.Media;
using System.Windows.Shapes;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Microsoft.Win32;
using MedicalImageAnalysis.Wpf.Services;
using MedicalImageAnalysis.Core.Interfaces;
using MedicalImageAnalysis.Core.Entities;
using MedicalImageAnalysis.Core.Models;
using DrawingSize = System.Drawing.Size;

namespace MedicalImageAnalysis.Wpf.Views
{
    /// <summary>
    /// 基于GDCM的DICOM查看器用户控件
    /// </summary>
    public partial class GdcmDicomViewer : UserControl
    {
        private readonly ILogger<GdcmDicomViewer> _logger;
        private readonly GdcmDicomService _gdcmDicomService;
        private readonly GdcmImageProcessor _gdcmImageProcessor;
        private readonly ISmartAnnotationService? _smartAnnotationService;

        private string? _currentDicomFile;
        private double _originalWindowCenter = 40;
        private double _originalWindowWidth = 400;
        private double _currentBrightness = 0.0;
        private BitmapSource? _currentImage;
        private List<UIElement> _aiDetectionOverlays = new();
        private BitmapSource? _originalImage;

        public GdcmDicomViewer()
        {
            InitializeComponent();

            // 从依赖注入容器获取服务
            var serviceProvider = ((App)System.Windows.Application.Current).ServiceProvider;
            _logger = serviceProvider?.GetService<ILogger<GdcmDicomViewer>>()
                     ?? throw new InvalidOperationException("无法获取日志服务");

            _gdcmDicomService = new GdcmDicomService(
                serviceProvider.GetService<ILogger<GdcmDicomService>>()!);

            _gdcmImageProcessor = new GdcmImageProcessor(
                serviceProvider.GetService<ILogger<GdcmImageProcessor>>()!);

            // 尝试获取AI服务
            _smartAnnotationService = serviceProvider?.GetService<ISmartAnnotationService>();

            InitializeViewer();
        }

        /// <summary>
        /// 初始化查看器
        /// </summary>
        private void InitializeViewer()
        {
            try
            {
                UpdateStatus("GDCM DICOM查看器已就绪");
                UpdateCurrentSettings();
                _logger.LogInformation("GDCM DICOM查看器初始化完成");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "初始化GDCM DICOM查看器失败");
                UpdateStatus($"初始化失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 打开DICOM文件按钮点击事件
        /// </summary>
        private async void OpenFileButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                var openFileDialog = new OpenFileDialog
                {
                    Title = "选择DICOM文件",
                    Filter = "DICOM文件 (*.dcm)|*.dcm|所有文件 (*.*)|*.*",
                    Multiselect = false
                };

                if (openFileDialog.ShowDialog() == true)
                {
                    await LoadDicomFileAsync(openFileDialog.FileName);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "打开DICOM文件失败");
                MessageBox.Show($"打开文件失败: {ex.Message}", "错误", 
                              MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// 加载DICOM文件
        /// </summary>
        private async Task LoadDicomFileAsync(string filePath)
        {
            try
            {
                ShowProgress("正在加载DICOM文件...");
                UpdateStatus($"正在加载: {System.IO.Path.GetFileName(filePath)}");

                // 验证文件
                if (!await _gdcmDicomService.IsValidDicomFileAsync(filePath))
                {
                    throw new InvalidOperationException("不是有效的DICOM文件");
                }

                _currentDicomFile = filePath;

                // 解析DICOM元数据
                var dicomInstance = await _gdcmDicomService.ParseDicomFileAsync(filePath);
                
                // 获取原始窗宽窗位
                _originalWindowCenter = dicomInstance.WindowCenter;
                _originalWindowWidth = dicomInstance.WindowWidth;

                // 更新窗宽窗位输入框
                WindowCenterTextBox.Text = _originalWindowCenter.ToString("F0");
                WindowWidthTextBox.Text = _originalWindowWidth.ToString("F0");

                // 提取并显示图像
                _originalImage = await _gdcmDicomService.ExtractImageAsync(filePath);
                _currentImage = _originalImage;
                if (_currentImage != null)
                {
                    DicomImage.Source = _currentImage;
                    UpdateImageInfo();
                }

                // 重置亮度值
                _currentBrightness = 0.0;
                BrightnessTextBox.Text = "0";

                // 显示DICOM信息
                await DisplayDicomInfoAsync(filePath);

                // 显示图像统计信息
                await DisplayImageStatisticsAsync(filePath);

                UpdateStatus($"已加载: {System.IO.Path.GetFileName(filePath)} ({dicomInstance.Columns}x{dicomInstance.Rows})");
                UpdateCurrentSettings();

                _logger.LogInformation("DICOM文件加载完成: {FilePath}", filePath);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "加载DICOM文件失败: {FilePath}", filePath);
                UpdateStatus($"加载失败: {ex.Message}");
                throw;
            }
            finally
            {
                HideProgress();
            }
        }

        /// <summary>
        /// 显示DICOM信息
        /// </summary>
        private async Task DisplayDicomInfoAsync(string filePath)
        {
            try
            {
                var dicomInfo = await _gdcmDicomService.GetDicomInfoAsync(filePath);
                var displayItems = dicomInfo.Select(kvp => new { Key = kvp.Key, Value = kvp.Value?.ToString() ?? "N/A" }).ToList();
                DicomInfoListView.ItemsSource = displayItems;
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "显示DICOM信息失败");
            }
        }

        /// <summary>
        /// 显示图像统计信息
        /// </summary>
        private async Task DisplayImageStatisticsAsync(string filePath)
        {
            try
            {
                var stats = await _gdcmImageProcessor.GetImageStatisticsAsync(filePath);
                
                MeanValueText.Text = $"平均值: {stats.Mean:F2}";
                MinValueText.Text = $"最小值: {stats.Min:F2}";
                MaxValueText.Text = $"最大值: {stats.Max:F2}";
                StdDevText.Text = $"标准差: {stats.StandardDeviation:F2}";
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "显示图像统计信息失败");
                MeanValueText.Text = "统计信息不可用";
                MinValueText.Text = "";
                MaxValueText.Text = "";
                StdDevText.Text = "";
            }
        }

        /// <summary>
        /// 窗宽窗位文本框变化事件
        /// </summary>
        private async void WindowLevel_TextChanged(object sender, TextChangedEventArgs e)
        {
            if (_currentDicomFile == null) return;

            try
            {
                if (double.TryParse(WindowCenterTextBox.Text, out double windowCenter) &&
                    double.TryParse(WindowWidthTextBox.Text, out double windowWidth))
                {
                    await ApplyWindowLevelAsync(windowCenter, windowWidth);
                }
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "应用窗宽窗位失败");
            }
        }

        /// <summary>
        /// 应用窗宽窗位
        /// </summary>
        private async Task ApplyWindowLevelAsync(double windowCenter, double windowWidth)
        {
            if (_currentDicomFile == null) return;

            try
            {
                // 应用窗宽窗位和当前亮度调整
                var adjustedImage = await _gdcmImageProcessor.ApplyWindowLevelWithExposureAsync(
                    _currentDicomFile, windowCenter, windowWidth, _currentBrightness);

                if (adjustedImage != null)
                {
                    DicomImage.Source = adjustedImage;
                    _currentImage = adjustedImage;
                    UpdateCurrentSettings();
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "应用窗宽窗位失败");
            }
        }

        /// <summary>
        /// 重置窗宽窗位按钮点击事件
        /// </summary>
        private async void ResetWindowButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                WindowCenterTextBox.Text = _originalWindowCenter.ToString("F0");
                WindowWidthTextBox.Text = _originalWindowWidth.ToString("F0");

                if (_currentDicomFile != null)
                {
                    await ApplyWindowLevelAsync(_originalWindowCenter, _originalWindowWidth);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "重置窗宽窗位失败");
            }
        }

        /// <summary>
        /// 肺窗预设按钮点击事件
        /// </summary>
        private async void LungWindowButton_Click(object sender, RoutedEventArgs e)
        {
            // 肺窗：窗宽1500，窗位-600
            WindowCenterTextBox.Text = "-600";
            WindowWidthTextBox.Text = "1500";
            await ApplyWindowLevelAsync(-600, 1500);
        }

        /// <summary>
        /// 软组织窗预设按钮点击事件
        /// </summary>
        private async void SoftTissueWindowButton_Click(object sender, RoutedEventArgs e)
        {
            // 软组织窗：窗宽400，窗位40
            WindowCenterTextBox.Text = "40";
            WindowWidthTextBox.Text = "400";
            await ApplyWindowLevelAsync(40, 400);
        }

        /// <summary>
        /// 骨窗预设按钮点击事件
        /// </summary>
        private async void BoneWindowButton_Click(object sender, RoutedEventArgs e)
        {
            // 骨窗：窗宽2000，窗位400
            WindowCenterTextBox.Text = "400";
            WindowWidthTextBox.Text = "2000";
            await ApplyWindowLevelAsync(400, 2000);
        }

        /// <summary>
        /// 脑窗预设按钮点击事件
        /// </summary>
        private async void BrainWindowButton_Click(object sender, RoutedEventArgs e)
        {
            // 脑窗：窗宽80，窗位40
            WindowCenterTextBox.Text = "40";
            WindowWidthTextBox.Text = "80";
            await ApplyWindowLevelAsync(40, 80);
        }

        /// <summary>
        /// 亮度输入框键盘事件
        /// </summary>
        private async void BrightnessTextBox_KeyDown(object sender, KeyEventArgs e)
        {
            if (e.Key == Key.Enter)
            {
                if (double.TryParse(BrightnessTextBox.Text, out double brightness))
                {
                    // 限制亮度范围在-100到100之间
                    brightness = Math.Clamp(brightness, -100, 100);
                    BrightnessTextBox.Text = brightness.ToString("F0");
                    _currentBrightness = brightness;
                    await ApplyBrightnessAdjustmentAsync();
                }
                else
                {
                    BrightnessTextBox.Text = _currentBrightness.ToString("F0");
                }
            }
        }

        /// <summary>
        /// 重置亮度按钮点击事件
        /// </summary>
        private async void ResetBrightnessButton_Click(object sender, RoutedEventArgs e)
        {
            BrightnessTextBox.Text = "0";
            _currentBrightness = 0.0;
            await ApplyBrightnessAdjustmentAsync();
        }

        /// <summary>
        /// 导出图像按钮点击事件
        /// </summary>
        private async void ExportImageButton_Click(object sender, RoutedEventArgs e)
        {
            if (_currentDicomFile == null || _currentImage == null)
            {
                MessageBox.Show("请先加载DICOM文件", "提示", MessageBoxButton.OK, MessageBoxImage.Information);
                return;
            }

            try
            {
                // 获取当前窗宽窗位值
                if (!double.TryParse(WindowCenterTextBox.Text, out double currentWindowCenter) ||
                    !double.TryParse(WindowWidthTextBox.Text, out double currentWindowWidth))
                {
                    MessageBox.Show("请确保窗宽窗位值有效", "提示", MessageBoxButton.OK, MessageBoxImage.Warning);
                    return;
                }

                // 生成包含窗宽窗位信息的文件名
                var baseFileName = System.IO.Path.GetFileNameWithoutExtension(_currentDicomFile);
                var windowInfo = $"_WW{currentWindowWidth:F0}_WC{currentWindowCenter:F0}";
                var defaultFileName = baseFileName + windowInfo;

                var saveFileDialog = new SaveFileDialog
                {
                    Title = "导出图像",
                    Filter = "PNG文件 (*.png)|*.png|JPEG文件 (*.jpg)|*.jpg|BMP文件 (*.bmp)|*.bmp|TIFF文件 (*.tiff)|*.tiff",
                    DefaultExt = "png",
                    FileName = defaultFileName
                };

                if (saveFileDialog.ShowDialog() == true)
                {
                    ShowProgress("正在导出图像...");

                    var extension = System.IO.Path.GetExtension(saveFileDialog.FileName).ToUpper();
                    var format = extension switch
                    {
                        ".PNG" => "PNG",
                        ".JPG" or ".JPEG" => "JPEG",
                        ".BMP" => "BMP",
                        ".TIFF" or ".TIF" => "TIFF",
                        _ => "PNG"
                    };

                    // 使用当前窗宽窗位值导出图像
                    var success = await _gdcmImageProcessor.ConvertDicomToImageWithWindowLevelAsync(
                        _currentDicomFile, saveFileDialog.FileName, format, currentWindowCenter, currentWindowWidth);

                    if (success)
                    {
                        UpdateStatus($"图像已导出: {System.IO.Path.GetFileName(saveFileDialog.FileName)} (WW:{currentWindowWidth:F0}/WC:{currentWindowCenter:F0})");
                        MessageBox.Show($"图像导出成功!\n窗宽/窗位: {currentWindowWidth:F0}/{currentWindowCenter:F0}", "成功", MessageBoxButton.OK, MessageBoxImage.Information);
                    }
                    else
                    {
                        throw new InvalidOperationException("图像导出失败");
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "导出图像失败");
                MessageBox.Show($"导出失败: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
            }
            finally
            {
                HideProgress();
            }
        }

        /// <summary>
        /// 更新图像信息显示
        /// </summary>
        private void UpdateImageInfo()
        {
            if (_currentImage != null)
            {
                ImageSizeText.Text = $"图像尺寸: {_currentImage.PixelWidth}x{_currentImage.PixelHeight}";
            }
        }

        /// <summary>
        /// 应用亮度调整
        /// </summary>
        private async Task ApplyBrightnessAdjustmentAsync()
        {
            if (_currentDicomFile == null || _originalImage == null) return;

            try
            {
                // 获取当前窗宽窗位
                if (double.TryParse(WindowCenterTextBox.Text, out double windowCenter) &&
                    double.TryParse(WindowWidthTextBox.Text, out double windowWidth))
                {
                    // 应用亮度调整的窗宽窗位
                    var adjustedImage = await _gdcmImageProcessor.ApplyWindowLevelWithExposureAsync(
                        _currentDicomFile, windowCenter, windowWidth, _currentBrightness);

                    if (adjustedImage != null)
                    {
                        DicomImage.Source = adjustedImage;
                        _currentImage = adjustedImage;
                        UpdateCurrentSettings();
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "应用亮度调整失败");
            }
        }

        /// <summary>
        /// 更新当前设置显示
        /// </summary>
        private void UpdateCurrentSettings()
        {
            if (double.TryParse(WindowCenterTextBox.Text, out double wc) &&
                double.TryParse(WindowWidthTextBox.Text, out double ww))
            {
                CurrentWindowText.Text = $"窗宽/窗位: {ww:F0}/{wc:F0}";
            }

            CurrentExposureText.Text = $"亮度: {_currentBrightness:F0}%";

            // WPF ScrollViewer没有ZoomFactor属性，使用固定值
            ZoomLevelText.Text = "缩放: 100%";

            // 显示像素值范围
            PixelRangeText.Text = "像素范围: -1024 ~ 3071 HU";
        }

        /// <summary>
        /// 更新状态栏
        /// </summary>
        private void UpdateStatus(string message)
        {
            StatusText.Text = message;
        }

        /// <summary>
        /// 显示进度条
        /// </summary>
        private void ShowProgress(string message)
        {
            ProgressText.Text = message;
            ProgressBar.Visibility = Visibility.Visible;
            ProgressBar.IsIndeterminate = true;
        }

        /// <summary>
        /// 隐藏进度条
        /// </summary>
        private void HideProgress()
        {
            ProgressText.Text = "";
            ProgressBar.Visibility = Visibility.Collapsed;
            ProgressBar.IsIndeterminate = false;
        }

        /// <summary>
        /// AI检测按钮点击事件
        /// </summary>
        private async void AIDetectionButton_Click(object sender, RoutedEventArgs e)
        {
            if (string.IsNullOrEmpty(_currentDicomFile))
            {
                MessageBox.Show("请先打开DICOM文件。", "提示",
                    MessageBoxButton.OK, MessageBoxImage.Information);
                return;
            }

            if (_smartAnnotationService == null)
            {
                MessageBox.Show("AI检测服务未初始化。", "错误",
                    MessageBoxButton.OK, MessageBoxImage.Error);
                return;
            }

            try
            {
                ShowProgress("正在进行AI检测...");

                // 清除之前的检测结果
                ClearAIDetectionOverlays();

                // 解析DICOM文件
                var dicomInstance = await _gdcmDicomService.ParseDicomFileAsync(_currentDicomFile);
                if (dicomInstance == null)
                {
                    MessageBox.Show("无法解析DICOM文件。", "错误",
                        MessageBoxButton.OK, MessageBoxImage.Error);
                    return;
                }

                // 创建智能标注配置 - 使用真实AI模型
                var config = new SmartAnnotationConfig
                {
                    EnableMultiModelFusion = true,
                    EnableQualityAssessment = true,
                    EnableSmartFiltering = true,
                    FilterConfig = new SmartFilterConfig
                    {
                        MinConfidenceThreshold = 0.5, // 提高阈值以获得更准确的检测结果
                        MinQualityThreshold = 0.6     // 提高质量阈值确保检测质量
                    },
                    ModelConfigs = new List<AutoAnnotationConfig>
                    {
                        new AutoAnnotationConfig
                        {
                            ModelPath = "yolo_medical_detection_model", // 使用真实的医学检测模型
                            ConfidenceThreshold = 0.5, // 与FilterConfig保持一致
                            IouThreshold = 0.45,
                            PreprocessingOptions = new ImagePreprocessingOptions
                            {
                                Normalize = true,
                                Resize = true,
                                TargetSize = (640, 640), // YOLO标准输入尺寸
                                InterpolationMethod = InterpolationMethod.Bilinear
                            }
                        }
                    }
                };

                // 执行AI检测
                var annotationResult = await _smartAnnotationService.GenerateSmartAnnotationsAsync(
                    dicomInstance, config, System.Threading.CancellationToken.None);

                if (annotationResult.Success && annotationResult.FinalAnnotations?.Any() == true)
                {
                    // 显示检测结果
                    DisplayAIDetectionResults(annotationResult.FinalAnnotations);

                    MessageBox.Show($"AI检测完成！\n检测到 {annotationResult.FinalAnnotations.Count} 个目标。",
                        "检测结果", MessageBoxButton.OK, MessageBoxImage.Information);
                }
                else
                {
                    MessageBox.Show("AI检测未发现任何目标。", "检测结果",
                        MessageBoxButton.OK, MessageBoxImage.Information);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "AI检测失败");
                MessageBox.Show($"AI检测失败：{ex.Message}", "错误",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
            finally
            {
                HideProgress();
            }
        }

        /// <summary>
        /// 显示AI检测结果
        /// </summary>
        private void DisplayAIDetectionResults(List<Annotation> annotations)
        {
            if (DicomImage.Source == null) return;

            var imageWidth = DicomImage.ActualWidth;
            var imageHeight = DicomImage.ActualHeight;

            foreach (var annotation in annotations)
            {
                // 创建检测框
                var rectangle = new WpfRectangle
                {
                    Stroke = WpfBrushes.Red,
                    StrokeThickness = 2,
                    Fill = WpfBrushes.Transparent,
                    StrokeDashArray = new DoubleCollection { 5, 3 }
                };

                // 转换归一化坐标到实际像素坐标
                var left = (annotation.BoundingBox.CenterX - annotation.BoundingBox.Width / 2) * imageWidth;
                var top = (annotation.BoundingBox.CenterY - annotation.BoundingBox.Height / 2) * imageHeight;
                var width = annotation.BoundingBox.Width * imageWidth;
                var height = annotation.BoundingBox.Height * imageHeight;

                rectangle.Width = width;
                rectangle.Height = height;
                Canvas.SetLeft(rectangle, left);
                Canvas.SetTop(rectangle, top);

                // 添加标签
                var label = new TextBlock
                {
                    Text = $"{annotation.Label} ({annotation.Confidence:F2})",
                    Background = WpfBrushes.Red,
                    Foreground = WpfBrushes.White,
                    Padding = new Thickness(2),
                    FontSize = 10
                };
                Canvas.SetLeft(label, left);
                Canvas.SetTop(label, top - 20);

                // 添加到画布
                ImageCanvas.Children.Add(rectangle);
                ImageCanvas.Children.Add(label);

                // 记录覆盖层元素
                _aiDetectionOverlays.Add(rectangle);
                _aiDetectionOverlays.Add(label);
            }
        }

        /// <summary>
        /// 清除AI检测覆盖层
        /// </summary>
        private void ClearAIDetectionOverlays()
        {
            foreach (var overlay in _aiDetectionOverlays)
            {
                ImageCanvas.Children.Remove(overlay);
            }
            _aiDetectionOverlays.Clear();
        }
    }
}
