using System;
using System.IO;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging;
using MedicalImageAnalysis.Wpf.Services;

namespace MedicalImageAnalysis.Test
{
    /// <summary>
    /// 测试DICOM文件路径修复
    /// </summary>
    class TestDicomFix
    {
        static async Task Main(string[] args)
        {
            // 创建日志记录器
            using var loggerFactory = LoggerFactory.Create(builder =>
                builder.AddConsole().SetMinimumLevel(LogLevel.Information));
            var logger = loggerFactory.CreateLogger<GdcmDicomService>();

            // 创建GDCM DICOM服务
            var gdcmService = new GdcmDicomService(logger);

            // 测试DICOM文件路径
            var testFilePath = @"E:\NovelCraft-C#\medical-imaging\Brain\DJ01.dcm";
            
            if (!File.Exists(testFilePath))
            {
                Console.WriteLine($"测试文件不存在: {testFilePath}");
                return;
            }

            try
            {
                Console.WriteLine($"正在测试DICOM文件: {testFilePath}");
                
                // 解析DICOM文件
                var dicomInstance = await gdcmService.ParseDicomFileAsync(testFilePath);
                
                if (dicomInstance == null)
                {
                    Console.WriteLine("❌ DICOM实例解析失败");
                    return;
                }

                // 检查文件路径是否正确设置
                Console.WriteLine($"SOP实例UID: {dicomInstance.SopInstanceUid}");
                Console.WriteLine($"文件路径: '{dicomInstance.FilePath}'");
                Console.WriteLine($"图像尺寸: {dicomInstance.Columns} x {dicomInstance.Rows}");
                Console.WriteLine($"窗宽/窗位: {dicomInstance.WindowWidth} / {dicomInstance.WindowCenter}");

                if (string.IsNullOrEmpty(dicomInstance.FilePath))
                {
                    Console.WriteLine("❌ 错误：文件路径为空！");
                }
                else if (dicomInstance.FilePath == testFilePath)
                {
                    Console.WriteLine("✅ 成功：文件路径设置正确！");
                }
                else
                {
                    Console.WriteLine($"❌ 错误：文件路径不匹配！期望: {testFilePath}, 实际: {dicomInstance.FilePath}");
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ 测试失败: {ex.Message}");
                Console.WriteLine($"堆栈跟踪: {ex.StackTrace}");
            }

            Console.WriteLine("按任意键退出...");
            Console.ReadKey();
        }
    }
}
