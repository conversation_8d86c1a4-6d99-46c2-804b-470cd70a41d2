<UserControl x:Class="MedicalImageAnalysis.Wpf.Views.DicomUploadView"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             mc:Ignorable="d"
             d:DesignHeight="600" d:DesignWidth="800">

    <ScrollViewer VerticalScrollBarVisibility="Auto">
        <StackPanel Margin="24">
            <!-- 页面标题 -->
            <TextBlock Text="DICOM 文件上传与处理" 
                     FontSize="28" 
                     FontWeight="Medium"
                     Margin="0,0,0,24"/>

            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="2*"/>
                    <ColumnDefinition Width="*"/>
                </Grid.ColumnDefinitions>

                <!-- 左侧：文件上传区域 -->
                <materialDesign:Card Grid.Column="0" 
                                   Margin="0,0,12,0"
                                   materialDesign:ElevationAssist.Elevation="Dp2">
                    <StackPanel Margin="24">
                        <TextBlock Text="选择 DICOM 文件" 
                                 FontSize="20" 
                                 FontWeight="Medium"
                                 Margin="0,0,0,16"/>

                        <!-- 拖拽上传区域 -->
                        <Border x:Name="DropZone"
                              Height="200"
                              BorderBrush="{DynamicResource MaterialDesignDivider}"
                              BorderThickness="2"
                              Background="{DynamicResource MaterialDesignCardBackground}"
                              AllowDrop="True"
                              Drop="DropZone_Drop"
                              DragOver="DropZone_DragOver"
                              DragLeave="DropZone_DragLeave">
                            <Border.Style>
                                <Style TargetType="Border">
                                    <Setter Property="BorderBrush" Value="{DynamicResource MaterialDesignDivider}"/>
                                    <Style.Triggers>
                                        <Trigger Property="IsMouseOver" Value="True">
                                            <Setter Property="BorderBrush" Value="{DynamicResource PrimaryHueMidBrush}"/>
                                        </Trigger>
                                    </Style.Triggers>
                                </Style>
                            </Border.Style>
                            <StackPanel VerticalAlignment="Center" 
                                      HorizontalAlignment="Center">
                                <materialDesign:PackIcon Kind="CloudUpload" 
                                                       Width="64" 
                                                       Height="64"
                                                       Foreground="{DynamicResource MaterialDesignBodyLight}"
                                                       Margin="0,0,0,16"/>
                                <TextBlock Text="拖拽 DICOM 文件到此处" 
                                         FontSize="16"
                                         HorizontalAlignment="Center"
                                         Margin="0,0,0,8"/>
                                <TextBlock Text="或者点击下方按钮选择文件" 
                                         FontSize="14"
                                         Foreground="{DynamicResource MaterialDesignBodyLight}"
                                         HorizontalAlignment="Center"/>
                            </StackPanel>
                        </Border>

                        <!-- 选择文件按钮 -->
                        <Button Style="{StaticResource MaterialDesignRaisedButton}"
                              HorizontalAlignment="Center"
                              Margin="0,16,0,0"
                              Click="SelectFiles_Click">
                            <StackPanel Orientation="Horizontal">
                                <materialDesign:PackIcon Kind="FolderOpen"
                                                       Width="20"
                                                       Height="20"
                                                       Margin="0,0,8,0"/>
                                <TextBlock Text="选择文件"/>
                            </StackPanel>
                        </Button>

                        <!-- 文件列表 -->
                        <materialDesign:Card x:Name="FileListCard"
                                           Margin="0,24,0,0"
                                           Visibility="Collapsed">
                            <StackPanel Margin="16">
                                <TextBlock Text="已选择的文件"
                                         FontSize="16"
                                         FontWeight="Medium"
                                         Margin="0,0,0,12"/>

                                <ListView x:Name="FileListView"
                                        MaxHeight="200">
                                    <ListView.View>
                                        <GridView>
                                            <GridViewColumn Header="文件名" Width="200">
                                                <GridViewColumn.CellTemplate>
                                                    <DataTemplate>
                                                        <StackPanel Orientation="Horizontal">
                                                            <materialDesign:PackIcon Kind="FileImage" 
                                                                                   Width="16" 
                                                                                   Height="16"
                                                                                   Margin="0,0,8,0"/>
                                                            <TextBlock Text="{Binding Name}"/>
                                                        </StackPanel>
                                                    </DataTemplate>
                                                </GridViewColumn.CellTemplate>
                                            </GridViewColumn>
                                            <GridViewColumn Header="大小" Width="80" DisplayMemberBinding="{Binding Size}"/>
                                            <GridViewColumn Header="状态" Width="80">
                                                <GridViewColumn.CellTemplate>
                                                    <DataTemplate>
                                                        <materialDesign:Chip Content="待处理" 
                                                                           Background="{DynamicResource MaterialDesignBodyLight}"/>
                                                    </DataTemplate>
                                                </GridViewColumn.CellTemplate>
                                            </GridViewColumn>
                                        </GridView>
                                    </ListView.View>
                                </ListView>

                                <!-- 操作按钮 -->
                                <StackPanel Orientation="Horizontal" 
                                          HorizontalAlignment="Right"
                                          Margin="0,16,0,0">
                                    <Button Content="清空" 
                                          Style="{StaticResource MaterialDesignOutlinedButton}"
                                          Margin="0,0,8,0"
                                          Click="ClearFiles_Click"/>
                                    <Button Content="开始处理" 
                                          Style="{StaticResource MaterialDesignRaisedButton}"
                                          Click="StartProcessing_Click"/>
                                </StackPanel>
                            </StackPanel>
                        </materialDesign:Card>
                    </StackPanel>
                </materialDesign:Card>

                <!-- 右侧：处理配置 -->
                <materialDesign:Card Grid.Column="1" 
                                   materialDesign:ElevationAssist.Elevation="Dp2">
                    <StackPanel Margin="24">
                        <TextBlock Text="处理配置" 
                                 FontSize="20" 
                                 FontWeight="Medium"
                                 Margin="0,0,0,16"/>

                        <!-- 配置选项 -->
                        <CheckBox x:Name="EnablePreprocessingCheckBox"
                                Content="启用图像预处理"
                                IsChecked="True"
                                Margin="0,8"/>
                        <CheckBox x:Name="EnableAIDetectionCheckBox"
                                Content="启用 AI 检测"
                                IsChecked="False"
                                Margin="0,8"/>
                        <CheckBox x:Name="EnableAnnotationValidationCheckBox"
                                Content="启用标注验证"
                                IsChecked="True"
                                Margin="0,8"/>
                        <CheckBox x:Name="EnableDatasetExportCheckBox"
                                Content="启用数据集导出"
                                IsChecked="False"
                                Margin="0,8"/>

                        <Separator Margin="0,16"/>

                        <!-- 置信度设置 -->
                        <TextBlock Text="置信度阈值" 
                                 FontWeight="Medium"
                                 Margin="0,0,0,8"/>
                        <Slider x:Name="ConfidenceSlider"
                              Minimum="0.1" 
                              Maximum="1.0" 
                              Value="0.5"
                              TickFrequency="0.1"
                              IsSnapToTickEnabled="True"/>
                        <TextBlock Text="{Binding ElementName=ConfidenceSlider, Path=Value, StringFormat=F1}" 
                                 HorizontalAlignment="Center"
                                 FontSize="12"
                                 Foreground="{DynamicResource MaterialDesignBodyLight}"/>

                        <Separator Margin="0,16"/>

                        <!-- 支持的格式 -->
                        <TextBlock Text="支持的格式" 
                                 FontWeight="Medium"
                                 Margin="0,0,0,8"/>
                        <StackPanel>
                            <StackPanel Orientation="Horizontal" Margin="0,4">
                                <materialDesign:PackIcon Kind="Check" 
                                                       Width="16" 
                                                       Height="16"
                                                       Foreground="Green"
                                                       Margin="0,0,8,0"/>
                                <TextBlock Text="DICOM (.dcm)"/>
                            </StackPanel>
                            <StackPanel Orientation="Horizontal" Margin="0,4">
                                <materialDesign:PackIcon Kind="Check" 
                                                       Width="16" 
                                                       Height="16"
                                                       Foreground="Green"
                                                       Margin="0,0,8,0"/>
                                <TextBlock Text="DICOM (.dicom)"/>
                            </StackPanel>
                            <TextBlock Text="最大文件大小: 500MB" 
                                     FontSize="12"
                                     Foreground="{DynamicResource MaterialDesignBodyLight}"
                                     Margin="0,8,0,0"/>
                        </StackPanel>
                    </StackPanel>
                </materialDesign:Card>
            </Grid>

            <!-- 处理进度 -->
            <materialDesign:Card x:Name="ProcessingCard" 
                               Margin="0,24,0,0"
                               Visibility="Collapsed">
                <StackPanel Margin="24">
                    <TextBlock Text="处理进度" 
                             FontSize="20" 
                             FontWeight="Medium"
                             Margin="0,0,0,16"/>
                    
                    <StackPanel HorizontalAlignment="Center">
                        <ProgressBar x:Name="ProcessingProgressBar"
                                   Width="300"
                                   Height="8"
                                   Style="{StaticResource MaterialDesignLinearProgressBar}"
                                   IsIndeterminate="True"/>
                        <TextBlock x:Name="ProcessingStatusText"
                                 Text="正在处理 DICOM 文件..."
                                 HorizontalAlignment="Center"
                                 Margin="0,16,0,0"/>
                    </StackPanel>
                </StackPanel>
            </materialDesign:Card>
        </StackPanel>
    </ScrollViewer>
</UserControl>
