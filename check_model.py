import torch
import sys

def check_model(model_path):
    try:
        print(f"正在检查模型: {model_path}")
        # 使用weights_only=False来加载YOLO模型
        model = torch.load(model_path, map_location='cpu', weights_only=False)
        print(f"模型类型: {type(model)}")

        if isinstance(model, dict):
            print(f"模型键: {list(model.keys())}")
            if 'model' in model:
                print(f"模型结构: {type(model['model'])}")
                # 检查模型的属性
                model_obj = model['model']
                if hasattr(model_obj, 'names'):
                    print(f"类别名称: {model_obj.names}")
                if hasattr(model_obj, 'nc'):
                    print(f"类别数量: {model_obj.nc}")
                if hasattr(model_obj, 'yaml'):
                    print(f"模型配置: {model_obj.yaml}")
            if 'names' in model:
                print(f"类别名称(根级): {model['names']}")
            if 'nc' in model:
                print(f"类别数量(根级): {model['nc']}")

        print("✅ 医学模型加载成功")
        return True
    except Exception as e:
        print(f"❌ 医学模型加载失败: {e}")
        return False

if __name__ == "__main__":
    model_path = 'yolo_ohif/yolo_ohif/models/weights/best.pt'
    check_model(model_path)
