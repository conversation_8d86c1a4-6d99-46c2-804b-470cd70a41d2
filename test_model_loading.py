#!/usr/bin/env python3
"""
测试YOLO模型加载
"""

import os
import sys
from pathlib import Path

def test_model_loading():
    """测试模型加载"""
    models_dir = Path("models")
    
    if not models_dir.exists():
        print("❌ models目录不存在")
        return False
    
    print(f"✅ models目录存在: {models_dir}")
    
    # 检查模型文件
    model_files = list(models_dir.glob("*.pt"))
    if not model_files:
        print("❌ 没有找到.pt模型文件")
        return False
    
    print(f"✅ 找到模型文件: {[f.name for f in model_files]}")
    
    # 测试加载YOLO模型
    try:
        from ultralytics import YOLO
        
        for model_file in model_files:
            print(f"🔄 测试加载模型: {model_file.name}")
            try:
                model = YOLO(str(model_file))
                print(f"✅ 模型加载成功: {model_file.name}")
                
                # 获取模型信息
                if hasattr(model, 'names'):
                    print(f"   类别数量: {len(model.names) if model.names else 0}")
                    if model.names:
                        print(f"   类别名称: {list(model.names.values())[:5]}...")  # 只显示前5个
                
                # 测试一个简单的推理（使用随机图像）
                import numpy as np
                from PIL import Image
                
                # 创建一个测试图像
                test_image = np.random.randint(0, 255, (640, 640, 3), dtype=np.uint8)
                test_image_pil = Image.fromarray(test_image)
                
                print(f"🔄 测试推理...")
                results = model.predict(test_image_pil, verbose=False)
                print(f"✅ 推理测试成功，检测到 {len(results[0].boxes) if results[0].boxes is not None else 0} 个对象")
                
            except Exception as e:
                print(f"❌ 模型加载失败: {model_file.name} - {e}")
                return False
    
    except ImportError:
        print("❌ ultralytics库未安装，请运行: pip install ultralytics")
        return False
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False
    
    print("🎉 所有模型测试通过！")
    return True

if __name__ == "__main__":
    success = test_model_loading()
    sys.exit(0 if success else 1)
